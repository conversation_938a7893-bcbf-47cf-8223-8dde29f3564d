const User = require("../models/userModel");
const Subscription = require("../models/subscriptionModel");

/**
 * Middleware to check if user has valid payment/subscription
 * Blocks access for users with pending payments
 * Allows access to essential routes like subscription and profile management
 */
module.exports = async (req, res, next) => {
  try {
    const userId = req.user?.userId || req.body?.userId;

    if (!userId) {
      console.log("❌ Payment Check Error: No userId found");
      return res.status(401).send({
        message: "Authentication required",
        success: false,
      });
    }

    // Get user data
    const user = await User.findById(userId);
    if (!user) {
      console.log("❌ Payment Check Error: User not found");
      return res.status(404).send({
        message: "User not found",
        success: false,
      });
    }

    // Skip payment check for admin users
    if (user.isAdmin) {
      console.log(`✅ Payment Check: Admin user ${user.username} - access granted`);
      return next();
    }

    // Skip payment check if user doesn't require payment
    if (!user.paymentRequired) {
      console.log(`✅ Payment Check: User ${user.username} - no payment required`);
      return next();
    }

    // Define routes that should be accessible even with pending payments
    const allowedRoutes = [
      // User profile management
      '/api/users/get-user-info',
      '/api/users/update-user-info',
      '/api/users/update-user-photo',

      // Payment and subscription management
      '/api/payment/create-invoice',
      '/api/payment/check-payment-status',
      '/api/payment/check-order-status',
      '/api/payment/webhook',

      // Plan information (needed for subscription page)
      '/api/plans',

      // Notification management
      '/api/notifications/online',
      '/api/notifications/offline',
      '/api/notifications/unread-count'
    ];

    // Check if current request is for an allowed route
    const requestPath = req.originalUrl || req.url;
    const isAllowedRoute = allowedRoutes.some(route => requestPath.includes(route));

    if (isAllowedRoute) {
      console.log(`✅ Payment Check: Allowing access to essential route ${requestPath} for user ${user.username}`);
      return next();
    }

    console.log(`🔍 Payment Check: Checking payment status for user ${user.username}`);

    // Check if user has any active paid subscriptions
    const activeSubscription = await Subscription.findOne({
      user: userId,
      status: "active",
      paymentStatus: "paid",
      endDate: { $ne: null, $gte: new Date().toISOString().split('T')[0] }
    }).populate("activePlan");

    if (!activeSubscription) {
      // Check if user has pending subscriptions
      const pendingSubscription = await Subscription.findOne({
        user: userId,
        paymentStatus: "pending"
      }).populate("activePlan");

      if (pendingSubscription) {
        console.log(`❌ Payment Check: User ${user.username} has pending payment - access blocked`);
        return res.status(403).send({
          message: "Your account is not activated. Please complete your payment to access this feature.",
          success: false,
          errorType: "PAYMENT_PENDING",
          requiresPayment: true,
          pendingSubscription: {
            plan: pendingSubscription.activePlan?.title,
            amount: pendingSubscription.activePlan?.discountedPrice || pendingSubscription.activePlan?.actualPrice,
            createdAt: pendingSubscription.createdAt
          }
        });
      } else {
        console.log(`❌ Payment Check: User ${user.username} requires subscription - access blocked`);
        return res.status(403).send({
          message: "This feature requires a subscription. Please subscribe to continue.",
          success: false,
          errorType: "SUBSCRIPTION_REQUIRED",
          requiresPayment: true
        });
      }
    }

    console.log(`✅ Payment Check: User ${user.username} has active subscription - access granted`);
    next();

  } catch (error) {
    console.error("❌ Payment Check Error:", error.message);
    res.status(500).send({
      message: "Payment verification failed",
      success: false,
      error: error.message
    });
  }
};
