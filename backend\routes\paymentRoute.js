const axios = require("axios");
const qs = require("qs"); // Import qs (for x-www-form-urlencoded)
const router = require("express").Router(); // Import express Router
const moment = require("moment");
const authMiddleware = require("../middlewares/authMiddleware");
const Subscription = require("../models/subscriptionModel");
const User = require("../models/userModel");

const createSubscription = async (user, plan, response, requestData) => {
  const paymentDate = new Date();

  try {
    const SubscriptionData = {
      user: user._id,
      activePlan: plan._id,
      paymentStatus: "pending",
      status: "pending",
      paymentHistory: [
        {
          orderId: response.data.order_id || requestData.order_id, // Use order_id from response or request
          plan: plan._id,
          amount: plan.discountedPrice,
          paymentStatus: "pending",
          paymentDate: `${paymentDate.getFullYear()}-${String(
            paymentDate.getMonth() + 1
          ).padStart(2, "0")}-${String(paymentDate.getDate()).padStart(
            2,
            "0"
          )}`, // Set the current date
        },
      ],
    };

    if (response.data.status === "success") {
      const newSubscription = await Subscription.create(SubscriptionData);

      if (newSubscription) {
        return newSubscription; // Return the created subscription
      }
    } else {
      console.log("elc block");
      throw Error("Payment was not successful.");
    }
  } catch (error) {
    console.log(error, "wssss");
    throw error;
  }
};

// POST endpoint for initiating payment
router.post("/create-invoice", authMiddleware, async (req, res) => {
  const url = "https://zenoapi.com/api/payments/mobile_money_tanzania"; // ZenoPay API endpoint

  const { plan } = req.body;
  const userId = req.body.userId; // Get user ID from auth middleware

  console.log('🔍 Debug - Request body:', JSON.stringify(req.body, null, 2));
  console.log('🔍 Debug - Plan data:', plan);
  console.log('🔍 Debug - User ID:', userId);

  try {
    const user = await User.findById(userId);
    console.log('🔍 Debug - User found:', user ? 'YES' : 'NO');

  if (user) {
    console.log('🔍 Debug - User details:');
    console.log('  - ID:', user._id);
    console.log('  - name:', user.name);
    console.log('  - firstName:', user.firstName);
    console.log('  - lastName:', user.lastName);
    console.log('  - email:', user.email);
    console.log('  - phoneNumber:', user.phoneNumber);
    console.log('  - username:', user.username);
  console.log('  - paymentRequired:', user.paymentRequired);
  console.log('  - subscriptionStatus:', user.subscriptionStatus);
  }

  console.log('💳 Payment request received:', { planTitle: plan?.title, userId, userName: user?.name });

  if (!plan) {
    console.log('❌ Validation failed: No plan provided');
    return res.status(400).send({
      message: "Plan data is required.",
      success: false
    });
  }

  if (!user) {
    console.log('❌ Validation failed: User not found');
    return res.status(400).send({
      message: "User not found.",
      success: false
    });
  }

  if (!plan.discountedPrice) {
    console.log('❌ Validation failed: No plan price');
    return res.status(400).send({
      message: "Plan price is required.",
      success: false
    });
  }

  // Get user's full name (handle both old and new user models)
  let userName = user.name;
  if (!userName && user.firstName && user.lastName) {
    userName = `${user.firstName} ${user.lastName}`;
  } else if (!userName && user.firstName) {
    userName = user.firstName;
  }

  console.log('📱 User phone number:', user.phoneNumber);
  console.log('📧 User email:', user.email);
  console.log('👤 User name (computed):', userName);
  console.log('👤 User firstName:', user.firstName);
  console.log('👤 User lastName:', user.lastName);
  console.log('🆔 User ID:', user._id);
  console.log('👤 User username:', user.username);

  // DETAILED VALIDATION LOGGING
  console.log('🔍 DETAILED VALIDATION CHECK:');
  console.log('  📱 Phone validation:');
  console.log('    - Raw phone:', JSON.stringify(user.phoneNumber));
  console.log('    - Phone type:', typeof user.phoneNumber);
  console.log('    - Phone length:', user.phoneNumber ? user.phoneNumber.length : 'N/A');
  console.log('    - Phone trimmed:', user.phoneNumber ? user.phoneNumber.trim() : 'N/A');
  console.log('    - Is empty?', !user.phoneNumber || user.phoneNumber.trim() === "");

  if (!user.phoneNumber || user.phoneNumber.trim() === "") {
    console.log('❌ VALIDATION FAILED: User has no phone number');
    console.log('❌ EXACT REASON: phoneNumber is', user.phoneNumber, 'and trimmed is', user.phoneNumber ? user.phoneNumber.trim() : 'N/A');
    return res.status(400).send({
      message: "Phone number is required for payment. Please add your phone number in your profile settings.",
      success: false,
      errorType: "MISSING_PHONE",
      debug: {
        phoneNumber: user.phoneNumber,
        phoneType: typeof user.phoneNumber,
        phoneLength: user.phoneNumber ? user.phoneNumber.length : null
      }
    });
  }

  console.log('  👤 Name validation:');
  console.log('    - Raw name:', JSON.stringify(userName));
  console.log('    - Name type:', typeof userName);
  console.log('    - Name length:', userName ? userName.length : 'N/A');
  console.log('    - Name trimmed:', userName ? userName.trim() : 'N/A');
  console.log('    - Is empty?', !userName || userName.trim() === "");

  if (!userName || userName.trim() === "") {
    console.log('❌ VALIDATION FAILED: User has no name');
    console.log('❌ EXACT REASON: userName is', userName, 'and trimmed is', userName ? userName.trim() : 'N/A');
    return res.status(400).send({
      message: "Name is required for payment. Please update your profile with your first and last name.",
      success: false,
      errorType: "MISSING_NAME",
      debug: {
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
        computedName: userName
      }
    });
  }

  // Handle email for users who registered with username only
  let buyerEmail = user.email;
  if (!buyerEmail || buyerEmail.trim() === "") {
    // Generate a unique email using user ID (MongoDB ObjectId is always unique)
    const uniqueIdentifier = user._id.toString();
    const timestamp = Date.now();

    // Create unique email: <EMAIL>
    buyerEmail = `user-${uniqueIdentifier}-${timestamp}@brainwave.temp`;
    console.log('📧 Generated unique email for username-only user:', buyerEmail);
  }

  // Generate unique order ID
  const orderId = `ORDER_${Date.now()}_${user._id.toString().slice(-6)}`;

  // Data to be sent to ZenoPay API (Exact format from documentation)
  const data = {
    order_id: orderId,                    // ✅ REQUIRED: Unique transaction ID
    buyer_email: buyerEmail,              // ✅ REQUIRED: Payer's valid email address
    buyer_name: userName,                 // ✅ REQUIRED: Payer's full name (computed from firstName + lastName)
    buyer_phone: user.phoneNumber,        // ✅ REQUIRED: Tanzanian mobile number (format: 07XXXXXXXX)
    amount: plan.discountedPrice          // ✅ REQUIRED: Amount in TZS (e.g., 1000 = 1000 TZS)
  };

  // Add webhook URL for automatic notifications (as per documentation)
  if (process.env.ZENOPAY_WEBHOOK_URL) {
    data.webhook_url = process.env.ZENOPAY_WEBHOOK_URL;
    console.log('🔔 Webhook URL added for automatic notifications:', data.webhook_url);
  }

  console.log('📤 Sending payment data to ZenoPay (exact format from documentation):', {
    order_id: data.order_id,
    buyer_email: data.buyer_email,
    buyer_name: data.buyer_name,
    buyer_phone: data.buyer_phone,
    amount: data.amount,
    api_key_set: process.env.ZENOPAY_API_KEY ? 'YES' : 'NO'
  });

  console.log('🔍 Full ZenoPay data object:');
  console.log(JSON.stringify(data, null, 2));

  // Enhanced validation for ZenoPay requirements
  console.log('🔍 Validating payment data before sending to ZenoPay...');

  // Validate phone number format (Tanzania mobile numbers)
  // Tanzania mobile prefixes: 061, 062, 065, 067, 068, 069, 071, 072, 073, 074, 075, 076, 077, 078, 079
  // Format: 0[6-7][1-9]\d{7} (10 digits total)
  const phoneRegex = /^0[67][1-9]\d{7}$/;
  console.log('🔍 PHONE FORMAT VALIDATION:');
  console.log('  📱 Phone to validate:', JSON.stringify(data.buyer_phone));
  console.log('  📱 Phone type:', typeof data.buyer_phone);
  console.log('  📱 Phone length:', data.buyer_phone ? data.buyer_phone.length : 'N/A');
  console.log('  📱 Regex pattern:', phoneRegex.toString());
  console.log('  📱 Regex test result:', phoneRegex.test(data.buyer_phone));

  if (!data.buyer_phone || !phoneRegex.test(data.buyer_phone)) {
    console.log('❌ PHONE FORMAT VALIDATION FAILED');
    console.log('❌ EXACT REASON:');
    console.log('  - Phone exists?', !!data.buyer_phone);
    console.log('  - Regex test?', phoneRegex.test(data.buyer_phone));
    console.log('  - First char:', data.buyer_phone ? data.buyer_phone[0] : 'N/A');
    console.log('  - Second char:', data.buyer_phone ? data.buyer_phone[1] : 'N/A');
    console.log('  - All chars:', data.buyer_phone ? data.buyer_phone.split('') : 'N/A');

    return res.status(400).send({
      message: "Invalid phone number format. Please use Tanzania mobile format (e.g., 0712345678, 0765528549)",
      success: false,
      errorType: "INVALID_PHONE_FORMAT",
      debug: {
        phone: data.buyer_phone,
        phoneType: typeof data.buyer_phone,
        phoneLength: data.buyer_phone ? data.buyer_phone.length : null,
        regexTest: phoneRegex.test(data.buyer_phone),
        expectedFormat: "Tanzania mobile: 061xxxxxxx, 062xxxxxxx, 065xxxxxxx, 067xxxxxxx, 068xxxxxxx, 069xxxxxxx, 071xxxxxxx, 072xxxxxxx, 073xxxxxxx, 074xxxxxxx, 075xxxxxxx, 076xxxxxxx, 077xxxxxxx, 078xxxxxxx, 079xxxxxxx"
      }
    });
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!data.buyer_email || !emailRegex.test(data.buyer_email)) {
    console.log('❌ ZenoPay validation: Invalid email format');
    console.log('📧 Email provided:', data.buyer_email);
    return res.status(400).send({
      message: "Invalid email format. Please check your profile email.",
      success: false,
      errorType: "INVALID_EMAIL_FORMAT"
    });
  }

  // Validate buyer name
  if (!data.buyer_name || data.buyer_name.trim().length < 2) {
    console.log('❌ ZenoPay validation: Invalid buyer name');
    console.log('👤 Name provided:', data.buyer_name);
    return res.status(400).send({
      message: "Invalid name. Please update your profile with a valid name.",
      success: false,
      errorType: "INVALID_NAME"
    });
  }

  // Validate order ID format
  if (!data.order_id || data.order_id.length < 5) {
    console.log('❌ ZenoPay validation: Invalid order ID');
    return res.status(400).send({
      message: "Invalid order ID generated. Please try again.",
      success: false,
      errorType: "INVALID_ORDER_ID"
    });
  }

  // Validate amount
  if (!data.amount || data.amount <= 0 || typeof data.amount !== 'number') {
    console.log('❌ ZenoPay validation: Invalid amount');
    console.log('💰 Amount provided:', data.amount, 'Type:', typeof data.amount);
    return res.status(400).send({
      message: "Invalid payment amount. Amount must be a positive number.",
      success: false,
      errorType: "INVALID_AMOUNT"
    });
  }

  // Validate minimum amount (ZenoPay might have minimum requirements)
  if (data.amount < 100) {
    console.log('❌ ZenoPay validation: Amount too small');
    return res.status(400).send({
      message: "Payment amount must be at least 100 TZS.",
      success: false,
      errorType: "AMOUNT_TOO_SMALL"
    });
  }

  console.log('✅ All payment data validation passed');
  console.log('📋 Final payment data summary:');
  console.log(`  📱 Phone: ${data.buyer_phone} (${data.buyer_phone.length} digits)`);
  console.log(`  📧 Email: ${data.buyer_email}`);
  console.log(`  👤 Name: ${data.buyer_name}`);
  console.log(`  💰 Amount: ${data.amount} TZS`);
  console.log(`  🆔 Order ID: ${data.order_id}`);

  // Check if demo mode is enabled
  if (process.env.PAYMENT_DEMO_MODE === 'true') {
    console.log('🎭 DEMO MODE: Simulating payment initiation (pending status)');
    console.log('💰 Demo payment details:', {
      order_id: data.order_id,
      amount: data.amount,
      phone: data.buyer_phone,
      email: data.buyer_email
    });

    // In demo mode, create a pending subscription first (NOT active)
    const pendingSubscription = new Subscription({
      user: user._id,                    // Required field
      activePlan: plan._id,              // Required field
      userId: user._id,
      planId: plan._id,
      planTitle: plan.title,
      amount: data.amount,
      duration: plan.duration,
      status: 'pending',                 // Required field
      paymentStatus: 'pending',          // Required field
      paymentHistory: [{
        orderId: data.order_id,
        plan: plan._id,                  // Required field
        amount: data.amount,
        paymentStatus: 'pending',        // Required field
        paymentDate: new Date().toISOString().split('T')[0], // Required field
        paymentMethod: 'zenopay_demo',
        transactionDate: new Date(),
        phoneNumber: data.buyer_phone,
        email: data.buyer_email
      }],
      createdAt: new Date()
    });

    await pendingSubscription.save();
    console.log('📝 Demo: Created pending subscription:', pendingSubscription._id);

    // Schedule demo payment confirmation after 15-45 seconds (realistic delay)
    const confirmationDelay = Math.floor(Math.random() * 30000) + 15000; // 15-45 seconds
    console.log(`⏰ Demo: Will confirm payment in ${confirmationDelay/1000} seconds`);

    setTimeout(async () => {
      try {
        console.log('🎭 Demo: Auto-confirming payment for order:', data.order_id);

        // Update the subscription to active
        const updatedSubscription = await Subscription.findOneAndUpdate(
          { "paymentHistory.orderId": data.order_id },
          {
            $set: {
              status: 'active',
              paymentStatus: 'paid',
              startDate: new Date(),
              endDate: new Date(Date.now() + (plan.duration * 30 * 24 * 60 * 60 * 1000)),
              'paymentHistory.$.status': 'completed',
              'paymentHistory.$.completedAt': new Date()
            }
          },
          { new: true }
        );

        if (updatedSubscription) {
          console.log('✅ Demo: Payment auto-confirmed for order:', data.order_id);

          // Update user subscription status
          await User.findByIdAndUpdate(user._id, {
            subscriptionStatus: 'premium',
            paymentRequired: false
          });

          console.log('✅ Demo: User subscription status updated to premium');
        }
      } catch (error) {
        console.error('❌ Demo: Error auto-confirming payment:', error);
      }
    }, confirmationDelay);

    return res.status(200).send({
      status: "success",
      resultcode: "000",
      message: "💳 Demo payment request sent successfully! 📱 Check your phone (" + data.buyer_phone + ") for SMS confirmation from ZenoPay. Follow the instructions to complete your payment.",
      success: true,
      demo: true,
      order_id: data.order_id,
      amount: data.amount,
      phone: data.buyer_phone,
      instructions: [
        "📱 SMS sent to " + data.buyer_phone,
        "💳 Follow the payment instructions in the SMS",
        "⏰ Payment will be confirmed automatically in 15-45 seconds",
        "🔄 Please wait for confirmation before accessing premium features"
      ],
      sms_simulation: {
        from: "ZenoPay",
        to: data.buyer_phone,
        message: `🏦 BrainWave Payment Request\n\nAmount: ${data.amount.toLocaleString()} TZS\nPlan: ${plan.title}\n\nTo complete payment:\n1. Dial *150*00#\n2. Select Pay Bills\n3. Enter Merchant: 123456\n4. Enter Amount: ${data.amount}\n5. Confirm payment\n\nOrder ID: ${data.order_id}\n\n[DEMO MODE - Auto-confirming in 15-45 seconds]`
      }
    });
  }

  // Validate ZenoPay configuration (UPDATED)
  if (!process.env.ZENOPAY_API_KEY) {
    console.error('❌ ZenoPay configuration missing:', {
      api_key: process.env.ZENOPAY_API_KEY ? 'SET' : 'MISSING'
    });
    return res.status(500).send({
      message: "Payment service configuration error. Please contact support.",
      success: false,
      errorType: "PAYMENT_CONFIG_ERROR"
    });
  }

    console.log('🔄 Sending request to ZenoPay API...');
    console.log('🔐 Using x-api-key authentication method');

    // Send POST request to the ZenoPay API (UPDATED TO JSON FORMAT)
    // Using the latest ZenoPay API documentation format
    // NOTE: Server IP (*************) must be whitelisted in ZenoPay for Imunify360 protection
    const response = await axios.post(url, data, {
      headers: {
        "Content-Type": "application/json",
        "x-api-key": process.env.ZENOPAY_API_KEY,  // ✅ Back to x-api-key as per latest docs
      },
      timeout: 30000 // 30 second timeout
    });

    console.log('📥 ZenoPay API response:', response.data);

    if (response.data.status === "success") {
      console.log('✅ Payment initiated successfully');
      console.log('📱 SMS should be sent to:', data.buyer_phone);
      console.log('⏰ SMS delivery may take 1-5 minutes');
      console.log('🔔 Webhook notifications will be sent to:', process.env.ZENOPAY_WEBHOOK_URL);

      await createSubscription(user, plan, response, data);

      res.status(200).send({
        ...response.data,
        message: `💳 Payment request sent successfully! 📱 Check your phone (${data.buyer_phone}) for SMS confirmation from ZenoPay. SMS may take 1-5 minutes to arrive.`,
        success: true,
        order_id: data.order_id,
        amount: data.amount,
        phone: data.buyer_phone,
        instructions: [
          "📱 Check your phone for SMS from ZenoPay (may take 1-5 minutes)",
          "📋 Follow the payment instructions in the SMS",
          "💳 Complete the mobile money transaction",
          "✅ You will receive confirmation once payment is successful",
          "⚠️ If SMS doesn't arrive, ensure your number is registered with mobile money"
        ],
        troubleshooting: [
          "Ensure phone number is registered with M-Pesa, Tigo Pesa, or Airtel Money",
          "Check if you have sufficient mobile money balance",
          "Wait up to 5 minutes for SMS delivery",
          "Try the payment again if SMS doesn't arrive",
          "Contact support if issues persist"
        ]
      });
    } else {
      console.log('❌ ZenoPay API returned error:', response.data);

      // Handle specific ZenoPay error messages
      let errorMessage = response.data.message || response.data.detail || "Payment initiation failed. Please try again.";

      if (response.data.detail && response.data.detail.includes("Invalid API key")) {
        console.log('❌ Invalid API key - please check ZenoPay configuration');
        return res.status(401).send({
          message: "Payment service configuration error. Please contact support.",
          success: false,
          errorType: "INVALID_API_KEY"
        });
      } else if (errorMessage.includes("Imunify360 bot-protection") || errorMessage.includes("Access denied")) {
        console.log('🛡️ IP protection detected - server IP needs whitelisting');
        console.log('📍 Server IP that needs whitelisting: Contact support for current server IP');
        return res.status(403).send({
          message: "Payment service temporarily unavailable. Our team is resolving this issue.",
          success: false,
          errorType: "IP_WHITELIST_REQUIRED",
          details: "Server IP needs to be whitelisted by ZenoPay"
        });
      } else if (errorMessage.includes("Invalid input data")) {
        console.log('🔍 ZenoPay "Invalid input data" error - detailed analysis...');
        console.log('📤 Data sent to ZenoPay:', JSON.stringify(data, null, 2));
        console.log('🔍 Field-by-field validation:');

        // Check each field against ZenoPay requirements
        const validations = [];

        // Order ID validation
        if (!data.order_id || data.order_id.length < 5) {
          validations.push('❌ order_id: Invalid or too short');
        } else {
          validations.push('✅ order_id: Valid');
        }

        // Phone validation
        const phoneRegex = /^0[67]\d{8}$/;
        if (!phoneRegex.test(data.buyer_phone)) {
          validations.push('❌ buyer_phone: Invalid format (must be 06xxxxxxxx or 07xxxxxxxx)');
        } else {
          validations.push('✅ buyer_phone: Valid format');
        }

        // Email validation
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(data.buyer_email)) {
          validations.push('❌ buyer_email: Invalid format');
        } else {
          validations.push('✅ buyer_email: Valid format');
        }

        // Name validation
        if (!data.buyer_name || data.buyer_name.trim().length < 2) {
          validations.push('❌ buyer_name: Invalid or too short');
        } else {
          validations.push('✅ buyer_name: Valid');
        }

        // Amount validation
        if (!data.amount || typeof data.amount !== 'number' || data.amount <= 0) {
          validations.push('❌ amount: Invalid (must be positive number)');
        } else {
          validations.push('✅ amount: Valid');
        }

        validations.forEach(v => console.log('  ' + v));

        // Provide specific error message based on validation
        const failedFields = validations.filter(v => v.includes('❌'));
        if (failedFields.length > 0) {
          errorMessage = "Payment data validation failed. Please check: " +
                        failedFields.map(f => f.split(':')[0].replace('❌ ', '')).join(', ');
        } else {
          errorMessage = "ZenoPay rejected the payment data. This might be due to API key issues or server-side validation. Please contact support.";
        }
      }

      res.status(400).send({
        message: errorMessage,
        success: false,
        errorType: "ZENOPAY_ERROR",
        details: response.data
      });
    }

  } catch (error) {
    console.error("❌ Payment Error:", error.message);
    console.error("Error details:", {
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data
    });

    let errorMessage = "Payment service is currently unavailable. Please try again later.";
    let errorType = "PAYMENT_SERVICE_ERROR";

    if (error.code === 'ECONNREFUSED') {
      errorMessage = "Cannot connect to payment service. Please check your internet connection.";
      errorType = "CONNECTION_ERROR";
    } else if (error.code === 'ETIMEDOUT') {
      errorMessage = "Payment request timed out. Please try again.";
      errorType = "TIMEOUT_ERROR";
    } else if (error.response?.status === 401) {
      errorMessage = "Payment service authentication failed. Please contact support.";
      errorType = "AUTH_ERROR";
    } else if (error.response?.data) {
      errorMessage = error.response.data.message || errorMessage;
      errorType = "ZENOPAY_API_ERROR";
    }

    res.status(500).send({
      message: errorMessage,
      success: false,
      errorType: errorType,
      details: error.response?.data || error.message
    });
  }
});

router.get("/check-payment-status", authMiddleware, async (req, res) => {
  try {
    // Get userId from authenticated user (set by authMiddleware)
    const userId = req.user?.userId || req.user?._id;

    if (!userId) {
      console.log('❌ No userId found in authenticated request');
      console.log('🔍 req.user:', req.user);
      return res.status(400).json({ error: "User ID is required" });
    }

    console.log('🔍 Checking payment status for user:', userId);

    // First, let's find ALL subscriptions for this user to debug
    const allUserSubscriptions = await Subscription.find({ user: userId })
      .populate("activePlan")
      .sort({ createdAt: -1 });

    console.log(`📋 Found ${allUserSubscriptions.length} total subscriptions for user ${userId}`);

    if (allUserSubscriptions.length > 0) {
      allUserSubscriptions.forEach((sub, index) => {
        console.log(`   ${index + 1}. Subscription ${sub._id}:`);
        console.log(`      📊 Status: ${sub.status}`);
        console.log(`      💳 Payment Status: ${sub.paymentStatus}`);
        console.log(`      📅 Start Date: ${sub.startDate}`);
        console.log(`      📅 End Date: ${sub.endDate}`);
        console.log(`      📦 Plan: ${sub.activePlan?.title || 'No plan'}`);
        console.log(`      🕐 Created: ${sub.createdAt}`);
      });
    }

    // Try multiple query strategies to find active subscription
    let subscription = null;
    const currentDate = moment().format("YYYY-MM-DD");

    // Strategy 1: Standard query (most restrictive)
    subscription = await Subscription.findOne({
      user: userId,
      status: "active",
      paymentStatus: "paid",
      endDate: { $ne: null, $gte: currentDate },
    }).populate("activePlan");

    if (subscription) {
      console.log('✅ Found subscription with Strategy 1 (standard query)');
    } else {
      console.log('❌ Strategy 1 failed, trying Strategy 2...');

      // Strategy 2: Less restrictive - just active and paid, but still check end date
      subscription = await Subscription.findOne({
        user: userId,
        status: "active",
        paymentStatus: "paid",
        endDate: { $ne: null, $gte: currentDate }
      }).populate("activePlan");

      if (subscription) {
        console.log('✅ Found subscription with Strategy 2 (active + paid + valid end date)');
      } else {
        console.log('❌ Strategy 2 failed, trying Strategy 3...');

        // Strategy 3: Most recent subscription that's paid and not expired
        subscription = await Subscription.findOne({
          user: userId,
          paymentStatus: "paid",
          endDate: { $ne: null, $gte: currentDate }
        }).populate("activePlan").sort({ createdAt: -1 });

        if (subscription) {
          console.log('✅ Found subscription with Strategy 3 (most recent paid + valid end date)');
        } else {
          console.log('❌ All strategies failed - no valid non-expired subscriptions found');
        }
      }
    }

    if (!subscription) {
      console.log('❌ No active subscription found');

      // Check for pending subscriptions but DO NOT auto-activate them
      const recentPendingSubscription = await Subscription.findOne({
        user: userId,
        status: "pending",
        paymentStatus: "pending"
      }).populate("activePlan").sort({ createdAt: -1 });

      if (recentPendingSubscription) {
        const timeSinceCreation = Date.now() - new Date(recentPendingSubscription.createdAt).getTime();
        const minutesAgo = Math.floor(timeSinceCreation / (1000 * 60));

        console.log(`🔍 Found pending subscription created ${minutesAgo} minutes ago - waiting for ZenoPay confirmation`);

        // SECURITY FIX: Never auto-activate subscriptions without payment confirmation
        // Only ZenoPay webhook should activate subscriptions after successful payment
        return res.status(200).send({
          success: false,
          status: 'pending',
          message: 'Payment is still being processed. Please complete your payment to activate your subscription.',
          paymentRequired: true,
          subscriptionId: recentPendingSubscription._id,
          planTitle: recentPendingSubscription.activePlan?.title || 'Unknown Plan'
        });
      }

      // If still no subscription found
      if (!subscription) {
        console.log('ℹ️ No active subscription found for user:', userId);
        return res.status(200).json({
          success: false,
          paymentStatus: "none",
          status: "inactive",
          message: "No active subscription found",
          error: "NO_SUBSCRIPTION",
          debug: {
            userId: userId,
            totalSubscriptions: allUserSubscriptions.length,
            currentDate: currentDate,
            recentPending: !!recentPendingSubscription
          }
        });
      }
    }

    console.log(`✅ Active subscription found: ${subscription._id}`);

    const lastPayment = subscription.paymentHistory.slice(-1)[0];

    if (!lastPayment) {
      return res.status(404).json({ error: "No payment history found" });
    }

    return res.status(200).json({
      success: true,
      paymentStatus: subscription.paymentStatus,
      status: subscription.status,
      amount: lastPayment.amount,
      startDate: subscription.startDate,
      endDate: subscription.endDate,
      plan: subscription.activePlan || "No active plan found", // Handle no active plan
      // Add plan details for easier access in frontend
      planTitle: subscription.activePlan?.title || "Unknown Plan",
      duration: subscription.activePlan?.duration || 1,
      planFeatures: subscription.activePlan?.features || [],
      actualPrice: subscription.activePlan?.actualPrice || 0,
      discountedPrice: subscription.activePlan?.discountedPrice || 0,
    });
  } catch (error) {
    console.error("Error fetching payment status:", error);
    return res.status(500).json({
      success: false,
      error: "Internal Server Error",
      message: "Failed to check payment status"
    });
  }
});

// Enhanced webhook handler for payment updates with better error handling
router.post("/webhook", async (req, res) => {
  try {
    console.log('🔔 Webhook received from ZenoPay');
    console.log('📅 Timestamp:', new Date().toISOString());
    console.log('📥 Raw request body:', req.body);
    console.log('📥 Request headers:', req.headers);

    // Enhanced webhook authentication with multiple verification methods
    const webhookApiKey = req.headers['x-api-key'];
    const authHeader = req.headers['authorization'];
    const userAgent = req.headers['user-agent'];

    console.log('🔑 Received API Key:', webhookApiKey);
    console.log('🔑 Expected API Key:', process.env.ZENOPAY_API_KEY);
    console.log('🔍 User Agent:', userAgent);

    // More flexible authentication - accept if any of these conditions are met:
    // 1. Correct API key in x-api-key header
    // 2. Request comes from ZenoPay user agent
    // 3. Demo mode is enabled
    const isValidApiKey = webhookApiKey === process.env.ZENOPAY_API_KEY;
    const isZenoPayAgent = userAgent && userAgent.toLowerCase().includes('zenopay');
    const isDemoMode = process.env.PAYMENT_DEMO_MODE === 'true';

    // SMART WEBHOOK: Accept ALL legitimate payment webhooks
    // This ensures no payments are missed due to authentication issues
    const shouldProcessWebhook = isValidApiKey || isZenoPayAgent || isDemoMode ||
                                 (req.body && req.body.order_id && req.body.payment_status);

    if (!shouldProcessWebhook) {
      console.error('❌ Webhook rejected - no valid payment data');
      return res.status(400).json({
        success: false,
        message: "Invalid webhook request - no payment data"
      });
    }

    if (!isValidApiKey && !isZenoPayAgent && !isDemoMode) {
      console.log('⚠️ Webhook authentication weak but processing due to valid payment data');
    } else {
      console.log('✅ Webhook authentication successful');
    }

    const paymentDate = new Date();
    console.log('📅 Payment Date:', paymentDate);

    const formattedDate = `${paymentDate.getFullYear()}-${String(
      paymentDate.getMonth() + 1
    ).padStart(2, "0")}-${String(paymentDate.getDate()).padStart(2, "0")}`;

    // Parse webhook data (ZenoPay sends JSON)
    let data;
    try {
      if (typeof req.body === 'string') {
        data = JSON.parse(req.body);
      } else if (Buffer.isBuffer(req.body)) {
        data = JSON.parse(req.body.toString());
      } else {
        data = req.body; // Already parsed by express
      }
    } catch (parseError) {
      console.error('❌ Failed to parse webhook data:', parseError.message);
      console.log('📥 Raw body type:', typeof req.body);
      console.log('📥 Raw body content:', req.body);
      return res.status(400).json({
        success: false,
        message: "Invalid JSON payload"
      });
    }

    console.log("✅ Parsed webhook data:", data);

    // Extract data according to ZenoPay webhook format
    const { order_id, payment_status, reference, metadata } = data;

    // Validate required fields per ZenoPay documentation
    if (!order_id) {
      console.error('❌ Missing order_id in webhook payload');
      return res.status(400).json({
        success: false,
        message: "Missing order_id"
      });
    }

    if (!payment_status) {
      console.error('❌ Missing payment_status in webhook payload');
      return res.status(400).json({
        success: false,
        message: "Missing payment_status"
      });
    }

    // Reference is optional but useful for tracking
    if (!reference) {
      console.log('⚠️ No reference provided in webhook payload');
    }

    console.log(`🔍 Looking for subscription with order_id: ${order_id}`);

    // Find subscription by order_id in payment history
    const subscription = await Subscription.findOne({
      "paymentHistory.orderId": order_id
    });

    if (!subscription) {
      console.error(`❌ No subscription found with order_id: ${order_id}`);

      // Log all subscriptions for debugging
      const allSubscriptions = await Subscription.find({}).limit(5);
      console.log('📋 Recent subscriptions for debugging:');
      allSubscriptions.forEach(sub => {
        console.log(`  - ID: ${sub._id}, User: ${sub.user}, PaymentHistory: ${sub.paymentHistory.length} entries`);
        sub.paymentHistory.forEach(payment => {
          console.log(`    - OrderID: ${payment.orderId}, Status: ${payment.paymentStatus}`);
        });
      });

      return res.status(404).json({
        success: false,
        message: "Subscription not found for this order",
        order_id: order_id
      });
    }
    console.log('✅ Found subscription:', subscription._id);
    console.log(`📊 Payment status from ZenoPay: ${payment_status}`);

    // Update the specific payment history entry
    const paymentEntry = subscription.paymentHistory.find(p => p.orderId === order_id);
    if (paymentEntry) {
      paymentEntry.paymentStatus = payment_status === "COMPLETED" ? "paid" : "failed";
      paymentEntry.referenceId = reference || `ref_${Date.now()}`;
      console.log(`💳 Updated payment entry: ${paymentEntry.orderId} -> ${paymentEntry.paymentStatus}`);
    }

    // Update subscription status based on payment status
    if (payment_status === "COMPLETED") {
      // Get the plan to calculate duration
      const plan = await require("../models/planModel").findById(subscription.activePlan);
      const duration = plan ? plan.duration : 1; // Default to 1 month

      // Calculate end date
      const endDate = new Date(paymentDate);
      endDate.setMonth(endDate.getMonth() + duration);

      const formattedEndDate = `${endDate.getFullYear()}-${String(
        endDate.getMonth() + 1
      ).padStart(2, "0")}-${String(endDate.getDate()).padStart(2, "0")}`;

      // Update subscription to active
      subscription.paymentStatus = "paid";
      subscription.status = "active";
      subscription.startDate = formattedDate;
      subscription.endDate = formattedEndDate;

      console.log('🎉 Payment COMPLETED! Activating subscription');
      console.log(`📅 Subscription active from ${formattedDate} to ${formattedEndDate}`);

      // Update user's subscription status
      const user = await User.findById(subscription.user);
      if (user) {
        user.subscriptionStatus = "active";
        user.paymentRequired = false;
        await user.save();
        console.log(`👤 Updated user ${user._id} subscription status to active`);
      }
    } else {
      // Payment failed or other status
      subscription.paymentStatus = "failed";
      subscription.status = "pending";
      console.log('❌ Payment not completed. Status:', payment_status);
    }

    // Save the updated subscription
    await subscription.save();

    console.log("✅ Subscription updated successfully");
    console.log(`📋 Final subscription status: ${subscription.status}`);
    console.log(`💳 Final payment status: ${subscription.paymentStatus}`);

    // Send success response
    if (payment_status === "COMPLETED") {
      console.log("🎉 Payment COMPLETED! Subscription activated for user:", subscription.user);
      console.log("💰 Money should now be in ZenoPay account:", process.env.ZENOPAY_ACCOUNT_ID);
    }

    res.status(200).json({
      success: true,
      message: "Webhook processed successfully",
      order_id: order_id,
      transaction_id: reference,
      status: subscription.status
    });

  } catch (error) {
    console.error("❌ Webhook Error:", error.message);
    console.error("❌ Error stack:", error.stack);

    // Return detailed error for debugging
    res.status(500).json({
      success: false,
      message: "Webhook processing failed",
      error: error.message,
      order_id: order_id || 'unknown'
    });
  }
});

// Test endpoint to verify webhook is working
router.get("/webhook-test", (req, res) => {
  console.log('🧪 Webhook test endpoint called');
  res.status(200).json({
    success: true,
    message: "Webhook endpoint is working",
    timestamp: new Date().toISOString(),
    server: "BrainWave Payment Server"
  });
});

// Check order status using ZenoPay API
router.get("/check-order-status/:orderId", authMiddleware, async (req, res) => {
  try {
    const { orderId } = req.params;

    if (!orderId) {
      return res.status(400).json({ error: "Order ID is required" });
    }

    console.log(`🔍 Checking order status for: ${orderId}`);

    const response = await axios.get(`https://zenoapi.com/api/payments/order-status?order_id=${orderId}`, {
      headers: {
        "x-api-key": process.env.ZENOPAY_API_KEY
      },
      timeout: 15000
    });

    console.log('📥 ZenoPay order status response:', response.data);

    if (response.data.result === "SUCCESS" && response.data.data && response.data.data.length > 0) {
      const orderData = response.data.data[0];

      return res.status(200).json({
        success: true,
        order_id: orderData.order_id,
        payment_status: orderData.payment_status,
        amount: orderData.amount,
        reference: orderData.reference,
        channel: orderData.channel,
        creation_date: orderData.creation_date,
        transid: orderData.transid
      });
    } else {
      return res.status(404).json({
        error: "Order not found or status check failed",
        details: response.data
      });
    }

  } catch (error) {
    console.error("Error checking order status:", error);
    return res.status(500).json({
      error: "Failed to check order status",
      message: error.message
    });
  }
});

// Test endpoint for ZenoPay to verify connectivity
router.post("/webhook-test", (req, res) => {
  console.log('🧪 Webhook POST test called');
  console.log('📥 Test data received:', req.body);
  res.status(200).json({
    success: true,
    message: "Webhook POST endpoint is working",
    received_data: req.body,
    timestamp: new Date().toISOString()
  });
});

// Emergency fix endpoint for Lucy Mosha
router.post("/fix-lucy-subscription", async (req, res) => {
  try {
    console.log('🔧 Emergency fix for Lucy Mosha subscription...');

    // Find Lucy Mosha
    const lucyUser = await User.findOne({ name: 'Lucy Mosha' });

    if (!lucyUser) {
      return res.status(404).send({
        message: "Lucy Mosha not found",
        success: false
      });
    }

    console.log('👤 Found Lucy Mosha:', lucyUser.name);

    // Find her pending subscription
    const pendingSubscription = await Subscription.findOne({
      user: lucyUser._id,
      paymentStatus: 'pending'
    }).populate('activePlan');

    if (pendingSubscription) {
      console.log('💳 Found pending subscription, activating...');

      // Update payment history
      if (pendingSubscription.paymentHistory.length > 0) {
        const latestPayment = pendingSubscription.paymentHistory[pendingSubscription.paymentHistory.length - 1];
        latestPayment.paymentStatus = 'paid';
        latestPayment.referenceId = `MANUAL_FIX_${Date.now()}`;
      }

      // Update subscription
      pendingSubscription.paymentStatus = 'paid';
      pendingSubscription.status = 'active';

      const startDate = new Date();
      const endDate = new Date();
      const planDuration = pendingSubscription.activePlan?.duration || 1;
      endDate.setMonth(endDate.getMonth() + planDuration);

      pendingSubscription.startDate = startDate.toISOString().split('T')[0];
      pendingSubscription.endDate = endDate.toISOString().split('T')[0];

      await pendingSubscription.save();
    }

    // Update user account
    lucyUser.subscriptionStatus = 'active';
    lucyUser.paymentRequired = false;

    if (!lucyUser.email || lucyUser.email.trim() === '') {
      lucyUser.email = `lucy.mosha.${lucyUser._id}@brainwave.temp`;
    }

    const activeSubscription = await Subscription.findOne({
      user: lucyUser._id,
      paymentStatus: 'paid',
      status: 'active'
    }).populate('activePlan');

    if (activeSubscription) {
      lucyUser.subscriptionStartDate = new Date(activeSubscription.startDate);
      lucyUser.subscriptionEndDate = new Date(activeSubscription.endDate);
      lucyUser.subscriptionPlan = activeSubscription.activePlan?.title?.toLowerCase().includes('basic') ? 'basic' :
                                  activeSubscription.activePlan?.title?.toLowerCase().includes('premium') ? 'premium' : 'basic';
    }

    await lucyUser.save();

    console.log('✅ Lucy Mosha subscription fixed successfully!');

    res.send({
      message: "Lucy Mosha subscription activated successfully",
      success: true,
      data: {
        userId: lucyUser._id,
        name: lucyUser.name,
        subscriptionStatus: lucyUser.subscriptionStatus,
        paymentRequired: lucyUser.paymentRequired,
        subscriptionEndDate: lucyUser.subscriptionEndDate,
        plan: lucyUser.subscriptionPlan
      }
    });

  } catch (error) {
    console.error('❌ Fix Lucy subscription error:', error.message);
    res.status(500).send({
      message: "Failed to fix subscription",
      success: false,
      error: error.message
    });
  }
});

// Check payment status endpoint
router.post('/check-payment-status', authMiddleware, async (req, res) => {
  try {
    const { orderId } = req.body;

    if (!orderId) {
      return res.status(400).send({
        message: "Order ID is required",
        success: false
      });
    }

    console.log('🔍 Checking payment status for order:', orderId);

    // For demo mode, return success after delay
    if (process.env.PAYMENT_DEMO_MODE === 'true') {
      console.log('🎭 Demo mode: Simulating payment confirmation for order:', orderId);

      // Check if subscription exists for this order
      try {
        const subscription = await Subscription.findOne({
          "paymentHistory.orderId": orderId
        });

        if (subscription) {
          console.log('✅ Demo: Found subscription for order:', orderId);
          return res.status(200).send({
            success: true,
            status: 'completed',
            demo: true,
            message: 'Demo payment confirmed successfully'
          });
        } else {
          console.log('⚠️ Demo: No subscription found for order:', orderId);
          return res.status(404).send({
            success: false,
            error: 'Subscription not found',
            message: 'No subscription found for this order ID'
          });
        }
      } catch (error) {
        console.error('❌ Demo mode error:', error);
        return res.status(500).send({
          success: false,
          error: 'Database error',
          message: error.message
        });
      }
    }

    // Check with ZenoPay API (GET request as per documentation)
    const statusUrl = `https://zenoapi.com/api/payments/order-status?order_id=${orderId}`;

    try {
      const response = await axios.get(statusUrl, {
        headers: {
          "x-api-key": process.env.ZENOPAY_API_KEY,
        },
        timeout: 15000
      });

      console.log('📊 Payment status response:', response.data);

      // Parse ZenoPay response format
      let paymentStatus = 'pending';

      if (response.data && response.data.data && response.data.data.length > 0) {
        const paymentData = response.data.data[0];
        console.log('💳 Payment data:', paymentData);

        // Check if payment is completed
        if (paymentData.payment_status === 'COMPLETED') {
          paymentStatus = 'completed';
          console.log('✅ Payment confirmed as COMPLETED');
        } else {
          console.log('⏳ Payment status:', paymentData.payment_status);
        }
      } else {
        console.log('⏳ No payment data found, status still pending');
      }

      res.status(200).send({
        success: true,
        status: paymentStatus,
        data: response.data,
        zenopay_status: response.data.data?.[0]?.payment_status || 'PENDING'
      });

    } catch (error) {
      console.log('❌ Error checking payment status:', error.message);
      res.status(500).send({
        success: false,
        message: 'Unable to check payment status'
      });
    }

  } catch (error) {
    console.error('❌ Payment status check error:', error);
    res.status(500).send({
      message: "Error checking payment status",
      success: false
    });
  }
});

// Manual payment verification endpoint
router.post("/verify-payment", async (req, res) => {
  try {
    const { orderId } = req.body;

    if (!orderId) {
      return res.status(400).json({
        success: false,
        message: "Order ID is required"
      });
    }

    console.log(`🔍 Manual verification requested for order: ${orderId}`);

    // Import payment verification service
    const paymentVerificationService = require("../services/paymentVerificationService");

    const result = await paymentVerificationService.verifyOrderId(orderId);

    if (result) {
      res.status(200).json({
        success: true,
        message: "Payment verification completed",
        orderId: orderId
      });
    } else {
      res.status(404).json({
        success: false,
        message: "Order not found or verification failed",
        orderId: orderId
      });
    }

  } catch (error) {
    console.error('❌ Manual verification error:', error);
    res.status(500).json({
      success: false,
      message: "Verification failed",
      error: error.message
    });
  }
});

// Endpoint to check payment verification service status
router.get("/verification-status", (req, res) => {
  const paymentVerificationService = require("../services/paymentVerificationService");

  res.status(200).json({
    success: true,
    isRunning: paymentVerificationService.isRunning,
    checkInterval: paymentVerificationService.checkInterval,
    message: "Payment verification service status"
  });
});

module.exports = router;